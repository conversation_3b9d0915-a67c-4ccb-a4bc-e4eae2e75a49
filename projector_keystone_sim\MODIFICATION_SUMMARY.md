# 投影仪标定代码修改总结

## 修改目标
根据用户要求，修改 `calib_real_at_0_angle_3.py` 代码实现，使得：
- **投影仪的cxp参数变为固定参数**（不再优化）
- **投影仪的yaw角度和pitch角度变为需要拟合的参数**

## 主要修改内容

### 1. 修改 `projector_pixel_to_world` 函数
**位置**: 第248-273行

**修改前**:
```python
def projector_pixel_to_world(u, v, Kp, D, proj_yaw_fixed=0.0):
    # 只支持yaw角度旋转
    R_proj = rot_y(proj_yaw_fixed)
```

**修改后**:
```python
def projector_pixel_to_world(u, v, Kp, D, proj_yaw=0.0, proj_pitch=0.0):
    # 支持yaw和pitch角度旋转
    R_proj = rot_y(proj_yaw) @ rot_x(proj_pitch)
```

### 2. 修改残差函数 `residual_params`
**位置**: 第315-355行

**修改前**:
- 参数: `[d_fx, d_fy, d_cx, d_cy, d_cxp]`
- 优化投影仪的cxp参数
- 投影仪yaw角度固定

**修改后**:
- 参数: `[d_fx, d_fy, d_cx, d_cy, proj_yaw, proj_pitch]`
- 投影仪内参完全固定（使用 `Kp = Kp0.copy()`）
- 优化投影仪的yaw和pitch角度

### 3. 修改主函数中的参数设置
**位置**: 第405-456行

**修改前**:
```python
# 固定投影仪yaw角度
proj_yaw_fixed = 0.0

# 优化参数: [d_fx, d_fy, d_cx, d_cy, d_cxp]
theta0 = np.array([0.0, 0.0, 0.0, 0.0, 0.0])

# 约束包含投影仪cxp的偏移范围
dcxp_min, dcxp_max = -3, 3
```

**修改后**:
```python
# 投影仪cxp现在是固定的
print(f"Projector cxp: {Kp[0,2]:.2f} (fixed)")

# 优化参数: [d_fx, d_fy, d_cx, d_cy, proj_yaw, proj_pitch]
theta0 = np.array([0.0, 0.0, 0.0, 0.0, 0.0, 0.0])

# 约束包含投影仪角度范围
proj_yaw_min, proj_yaw_max = -30.0, 30.0
proj_pitch_min, proj_pitch_max = -30.0, 30.0
```

### 4. 修改优化调用和结果处理
**位置**: 第460-492行

**修改前**:
```python
# 优化调用包含proj_yaw_fixed参数
args=(fixed, proj_pts, cam_obs, pitch_fixed, yaw_fixed, roll_fixed, D_fixed, proj_yaw_fixed)

# 解析结果
d_fx_est, d_fy_est, d_cx_est, d_cy_est, d_cxp_est = result.x

# 计算估计的投影仪内参
cxp_est = cxp0 + d_cxp_est
```

**修改后**:
```python
# 优化调用不再需要proj_yaw_fixed参数
args=(fixed, proj_pts, cam_obs, pitch_fixed, yaw_fixed, roll_fixed, D_fixed)

# 解析结果
d_fx_est, d_fy_est, d_cx_est, d_cy_est, proj_yaw_est, proj_pitch_est = result.x

# 投影仪内参保持固定
Kp_est = Kp.copy()
```

### 5. 更新所有相关函数调用
修改了以下函数中的 `projector_pixel_to_world` 调用，添加了pitch角度参数：
- 重投影误差计算
- `compute_camera_projection_at_new_distance` 函数
- `fit_projector_yaw_angle` 函数
- `fit_projector_distance` 函数

## 验证结果

创建了测试脚本 `test_modified_calib.py` 验证修改的正确性：

1. ✅ `projector_pixel_to_world` 函数正确支持yaw和pitch角度
2. ✅ `residual_params` 函数正确处理新的6参数格式
3. ✅ 所有函数调用兼容新的参数格式

## 使用说明

修改后的代码现在会：
1. **固定投影仪的cxp参数**（使用初始值，不再优化）
2. **优化投影仪的yaw和pitch角度**（范围±30度）
3. **继续优化相机内参的增量**（fx, fy, cx, cy）
4. **保持相机姿态和距离参数固定**

优化参数从5个变为6个：
- 原来: `[d_fx, d_fy, d_cx, d_cy, d_cxp]`
- 现在: `[d_fx, d_fy, d_cx, d_cy, proj_yaw, proj_pitch]`
