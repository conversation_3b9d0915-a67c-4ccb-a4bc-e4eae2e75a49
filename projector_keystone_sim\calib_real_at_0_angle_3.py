import json
import math
from dataclasses import dataclass

import cv2
import numpy as np

from scipy.optimize import least_squares


def get_corners_from_left_top(width, height, square_w, square_h, squares_x, squares_y):
    """
    按 从上到下、每行从左到右 的顺序生成角点坐标。
    这里角点是所有网格交点，因此有 (squares_x+1) * (squares_y+1) 个角点。
    """
    corners = []
    for j in range(squares_y + 1):         # 从上到下
        for i in range(squares_x + 1):     # 每行从左到右
            x = i * square_w
            y = j * square_h
            # 防止落在 width / height 边界之外（最后一个点可能等于 width / height）
            x = min(x, width - 1)
            y = min(y, height - 1)
            corners.append((x, y))
    return corners


def get_proj_pts():
    width, height = 1920, 1080
    # squares_x, squares_y = 12, 8
    # square_w = width // squares_x
    # square_h = height // squares_y

    # # 生成角点（从左到右，从上到下）
    # corners = get_corners_from_left_top(
    #     width, height,
    #     square_w, square_h,
    #     squares_x, squares_y
    # )
    # corners = np.array(corners).reshape(squares_y + 1, squares_x + 1, 2)
    # # 去掉外圈，只保留棋盘内部角点
    # corners = corners[1:-1, 1:-1, :].reshape(-1, 2)
    NUM_CALIB_POINTS = 25
    corners = []
    for i in range(NUM_CALIB_POINTS):
        ip_x = ((i % 5) *2 + 11) * 54 + 95
        ip_y = ((i // 5) * 2 + 5) * 54 + 52
        ip = (ip_x, ip_y)
        corners.append(ip)
    corners = np.array(corners, dtype=np.float32)

    return corners



def get_proj_pts_more():
    width, height = 1920, 1080
    # squares_x, squares_y = 12, 8
    # square_w = width // squares_x
    # square_h = height // squares_y

    # # 生成角点（从左到右，从上到下）
    # corners = get_corners_from_left_top(
    #     width, height,
    #     square_w, square_h,
    #     squares_x, squares_y
    # )
    # corners = np.array(corners).reshape(squares_y + 1, squares_x + 1, 2)
    # # 去掉外圈，只保留棋盘内部角点
    # corners = corners[1:-1, 1:-1, :].reshape(-1, 2)
    NUM_CALIB_POINTS = 40
    corners = []
    for i in range(NUM_CALIB_POINTS):
        ip_x = ((i % 8) *4 + 2) * 54 + 95
        ip_y = ((i // 8) * 4 + 1) * 54 + 52
        ip = (ip_x, ip_y)
        corners.append(ip)
    corners = np.array(corners, dtype=np.float32)

    return corners


# ---------- 旋转工具 ----------
def rot_x(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    # camera-to-world rotation
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)


def get_corner(img, corner_init):
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    corner_init = np.array(corner_init, dtype=np.float32)
    corners_subpix = cv2.cornerSubPix(
            gray, corner_init, (7, 7), (-1, -1),
            criteria=(cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
        )
    
    return corners_subpix

def get_point(img, img_path):
    corner_dict = {"cam_200cm_0d.jpg": np.array([[508, 245], [562, 247], [616, 244], [671, 243], [728, 243], 
                                                 [505, 297], [561, 294], [618, 294], [673, 294], [728, 294], 
                                                 [503, 348], [558, 347], [616, 347], [671, 347], [731, 347],
                                                   [499, 402], [556, 402], [614, 401], [670, 403], [732, 402], 
                                                   [496, 458], [556, 458], [615, 458], [673, 460], [733, 458]], dtype=np.float32),
                    "cam_100cm_0d.jpg": np.array([[467, 243], [522, 242], [576, 243], [631, 242], [686, 241], 
                                                 [463, 294], [518, 294], [574, 293], [630, 293], [686, 292], 
                                                 [458, 348], [515, 345], [573, 345], [630, 345], [688, 345],
                                                   [456, 401], [513, 401], [572, 401], [630, 401], [688, 400], 
                                                   [451, 458], [511, 457], [570, 457], [630, 457], [690, 457]], dtype=np.float32),
                    "cam_110cm_0d.jpg": np.array([[249, 151], [350, 151], [455, 149], [559, 147], [665, 146], [772, 145], [880, 143], [988, 141], 
                                                [235, 246], [340, 245], [449, 245], [556, 243], [666, 242], [776, 241], [888, 240], [1002, 239], 
                                                [218, 348], [329, 347], [439, 347], [552, 346], [666, 346], [782, 346], [899, 345], [1016, 345], 
                                                [201, 458], [314, 458], [430, 458], [548, 458], [667, 458], [788, 459], [909, 459], [1032, 459], 
                                                [182, 577], [302, 579], [421, 581], [544, 581], [669, 582], [793, 581], [920, 583], [1048, 584]], dtype=np.float32),
                    "cam_120cm_0d.jpg": np.array([[481, 244], [535, 244], [589, 243], [644, 243], [700, 240], 
                                                  [477, 295], [533, 294], [588, 294], [646, 293], [701, 293], 
                                                  [472, 346], [531, 347], [587, 346], [644, 346], [702, 346], 
                                                  [470, 401], [526, 401], [585, 401], [643, 401], [702, 401], 
                                                  [467, 457], [526, 458], [584, 457], [643, 457], [704, 458]], dtype=np.float32),
                    "cam_150cm_0d.jpg": np.array([[495, 244], [548, 244], [603, 243], [658, 242], [713, 241], 
                                                  [490, 295], [547, 295], [602, 295], [658, 291], [715, 294], 
                                                  [487, 347], [543, 346], [601, 346], [658, 347], [716, 346], 
                                                  [485, 402], [542, 401], [601, 401], [659, 402], [718, 400], 
                                                  [481, 460], [539, 457], [601, 457], [658, 458], [720, 458]], dtype=np.float32),
                    "cam_150cm_10d_l.jpg": np.array([[497, 245], [550, 245], [604, 244], [658, 244], [714, 241], 
                                                     [493, 295], [548, 295], [602, 294], [658, 294], [713, 294], 
                                                     [490, 347], [548, 347], [601, 347], [659, 346], [716, 347], 
                                                     [487, 402], [546, 401], [602, 402], [659, 401], [718, 402], 
                                                     [483, 460], [542, 457], [601, 458], [659, 458], [718, 458]], dtype=np.float32), 
                    "cam_150cm_10d_r.jpg": np.array([[495, 247], [549, 244], [605, 243], [660, 243], [716, 243], 
                                                     [492, 294], [548, 294], [604, 294], [661, 294], [719, 294], 
                                                     [488, 347], [546, 346], [603, 347], [660, 346], [719, 346], 
                                                     [485, 402], [543, 402], [603, 401], [661, 400], [722, 403], 
                                                     [481, 458], [541, 458], [601, 458], [664, 457], [722, 460]], dtype=np.float32), 
                    "cam_150cm_15d_l.jpg":  np.array([[499, 244], [552, 244], [605, 243], [659, 243], [711, 242], 
                                                      [495, 295], [551, 296], [605, 295], [659, 295], [715, 293], 
                                                      [492, 347], [549, 348], [603, 346], [660, 347], [716, 347], 
                                                      [490, 403], [546, 403], [602, 402], [661, 402], [719, 401], 
                                                      [487, 458], [544, 458], [601, 457], [661, 458], [720, 460]], dtype=np.float32),
                    "cam_150cm_20d_r.jpg": np.array([[495, 247], [552, 244], [607, 245], [665, 243], [720, 244], 
                                                     [493, 294], [548, 294], [609, 294], [662, 294], [720, 294], 
                                                     [490, 346], [546, 348], [604, 346], [664, 347], [724, 347], 
                                                     [486, 403], [547, 402], [605, 402], [665, 403], [725, 401], 
                                                     [483, 458], [543, 456], [605, 457], [665, 457], [724, 458]], dtype=np.float32)}
    corner_init = corner_dict[img_path]
    corners_subpix = get_corner(img, corner_init)
    return corners_subpix

def get_point_more(img, img_path):   
    corner_dict = {
                    "cam_100cm_0d.jpg": np.array([[242, 151], [345, 150], [449, 149], [553, 147], [659, 145], [765, 143], [873, 142], [982, 140], 
                                                  [226, 245], [333, 245], [441, 244], [549, 243], [659, 241], [769, 241], [882, 239], [995, 238], 
                                                  [211, 348], [321, 347], [432, 348], [545, 346], [659, 345], [775, 344], [891, 345], [1009, 344], 
                                                  [193, 458], [307, 458], [423, 458], [540, 457], [659, 458], [780, 459], [902, 459], [1025, 458], 
                                                  [174, 577], [292, 578], [413, 580], [536, 579], [660, 581], [784, 581], [913, 583], [1040, 583]], dtype=np.float32),
                    "cam_110cm_0d.jpg": np.array([[249, 151], [350, 151], [455, 149], [559, 147], [665, 146], [772, 145], [880, 143], [988, 141], 
                                                  [235, 246], [340, 245], [449, 245], [556, 243], [666, 242], [776, 241], [888, 240], [1002, 239], 
                                                  [218, 348], [329, 347], [439, 347], [552, 346], [666, 346], [782, 346], [899, 345], [1016, 345], 
                                                  [201, 458], [314, 458], [430, 458], [548, 458], [667, 458], [788, 459], [909, 459], [1032, 459], 
                                                  [182, 577], [302, 579], [421, 581], [544, 581], [669, 582], [793, 581], [920, 583], [1048, 584]], dtype=np.float32),
                    "cam_200cm_0d.jpg": np.array([[281, 153], [384, 152], [486, 151], [591, 150], [697, 147], [803, 148], [911, 145], [1021, 143], 
                                                  [266, 248], [373, 248], [480, 246], [589, 245], [699, 243], [809, 243], [920, 242], [1034, 241], 
                                                  [253, 349], [363, 349], [473, 348], [587, 348], [701, 347], [816, 347], [933, 347], [1050, 346], 
                                                  [236, 459], [350, 459], [467, 459], [583, 460], [703, 460], [823, 460], [944, 461], [1066, 460], 
                                                  [219, 579], [338, 579], [458, 581], [580, 581], [705, 581], [830, 584], [957, 584], [1084, 584]], dtype=np.float32),
                    "cam_150cm_0d.jpg": np.array([[267, 153], [369, 151], [474, 151], [578, 147], [683, 147], [790, 146], [898, 144], [1007, 142], 
                                                  [253, 248], [360, 245], [466, 247], [575, 244], [685, 243], [796, 242], [908, 241], [1021, 240], 
                                                  [238, 349], [349, 348], [460, 348], [573, 347], [686, 346], [802, 346], [918, 346], [1036, 346], 
                                                  [222, 458], [335, 458], [451, 458], [569, 458], [688, 460], [808, 460], [931, 460], [1053, 460], 
                                                  [204, 578], [322, 578], [445, 581], [565, 581], [689, 582], [817, 583], [941, 583], [1069, 584]], dtype=np.float32),
                    "cam_150cm_0d.jpg": np.array([[267, 153], [369, 151], [474, 151], [578, 147], [683, 147], [790, 146], [898, 144], [1007, 142], 
                                                  [253, 248], [360, 245], [466, 247], [575, 244], [685, 243], [796, 242], [908, 241], [1021, 240], 
                                                  [238, 349], [349, 348], [460, 348], [573, 347], [686, 346], [802, 346], [918, 346], [1036, 346], 
                                                  [222, 458], [335, 458], [451, 458], [569, 458], [688, 460], [808, 460], [931, 460], [1053, 460], 
                                                  [204, 578], [322, 578], [445, 581], [565, 581], [689, 582], [817, 583], [941, 583], [1069, 584]], dtype=np.float32),
                    "cam_150cm_10d_l.jpg": np.array([[273, 153], [375, 152], [476, 151], [580, 149], [683, 147], [788, 145], [896, 144], [1003, 142], 
                                                     [259, 248], [364, 248], [469, 245], [577, 243], [684, 243], [795, 242], [905, 242], [1016, 241], 
                                                     [244, 350], [352, 348], [463, 348], [574, 347], [686, 347], [799, 346], [914, 346], [1031, 346], 
                                                     [227, 460], [339, 460], [457, 460], [571, 458], [687, 460], [806, 460], [927, 460], [1047, 460], 
                                                     [211, 578], [328, 579], [447, 579], [567, 579], [691, 582], [814, 582], [939, 583], [1063, 584]], dtype=np.float32), 
                    "cam_150cm_10d_r.jpg": np.array([[263, 153], [367, 151], [472, 150], [579, 149], [687, 146], [795, 147], [904, 144], [1016, 142], 
                                                     [251, 245], [358, 247], [466, 247], [577, 244], [688, 243], [800, 243], [914, 241], [1028, 241], 
                                                     [233, 349], [348, 349], [459, 348], [573, 347], [689, 347], [808, 347], [926, 344], [1043, 346], 
                                                     [217, 457], [334, 460], [453, 460], [570, 460], [691, 457], [814, 460], [936, 460], [1061, 460], 
                                                     [200, 578], [319, 579], [443, 581], [568, 581], [694, 581], [822, 584], [950, 585], [1080, 585]], dtype=np.float32), 
                    "cam_150cm_15d_l.jpg":  np.array([[274, 152], [375, 152], [478, 150], [582, 149], [685, 146], [790, 147], [895, 144], [1001, 142], 
                                                      [262, 247], [366, 247], [471, 247], [578, 243], [686, 243], [794, 242], [903, 241], [1015, 240], 
                                                      [246, 349], [357, 348], [466, 348], [576, 347], [687, 347], [801, 348], [914, 346], [1030, 346], 
                                                      [232, 458], [343, 458], [458, 460], [573, 458], [688, 458], [808, 460], [926, 460], [1045, 460], 
                                                      [214, 579], [330, 579], [450, 579], [569, 581], [691, 581], [813, 584], [938, 584], [1064, 584]], dtype=np.float32),
                    "cam_150cm_20d_r.jpg": np.array([[262, 152], [368, 151], [475, 150], [582, 150], [688, 147], [800, 147], [910, 145], [1021, 144], 
                                                     [249, 248], [358, 247], [468, 247], [578, 244], [692, 243], [805, 244], [920, 242], [1035, 241], 
                                                     [233, 349], [348, 349], [461, 347], [576, 347], [693, 348], [811, 347], [931, 347], [1052, 346], 
                                                     [218, 457], [334, 456], [452, 458], [573, 458], [695, 460], [819, 460], [943, 461], [1066, 460], 
                                                     [199, 579], [321, 579], [447, 579], [570, 579], [698, 583], [826, 584], [955, 583], [1085, 586]], dtype=np.float32)}
    if img_path is None:
        return corner_dict
    
    corner_init = corner_dict[img_path]
    corners_subpix = get_corner(img, corner_init)
    return corners_subpix


def get_cam_K():
    # 作为初始值，用标定得到的 Kc0
    # return np.array(
    #     [[820.295, 0,   601.496],
    #      [0,      821.146, 403.593],
    #      [0,      0,   1]],
    #     dtype=np.float64
    # )
    return  np.array(
        [[838.295, 0,        618.496],
         [0,       851.146,  403.593],
         [0,       0,        1],
        ])


# ---------- 投影仪内参 ----------
def intrinsics_from_throw_ratio(w, h, throw_ratio, cx, cy):
    fovx = 2.0 * math.atan(1.0 / (2.0 * throw_ratio))  # rad
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)


def get_projector_K():
    Wp, Hp = 1920, 1080
    throw_ratio = 0.8
    cxp, cyp = Wp / 2, Hp
    Kp = intrinsics_from_throw_ratio(Wp, Hp, throw_ratio, cxp, cyp)
    return Kp


# ---------- 从投影仪像素 -> 墙面 3D ----------
def projector_pixel_to_world(u, v, Kp, D, proj_yaw=0.0, proj_pitch=0.0):
    """
    u, v: (N,) 投影仪像素坐标
    D: 投影中心到墙面的距离 (m)
    proj_yaw: 投影仪的yaw角度 (度)
    proj_pitch: 投影仪的pitch角度 (度)
    返回: (N,3) 世界坐标点 (X,Y,0)
    """
    uv1 = np.vstack([u, v, np.ones_like(u, dtype=np.float64)])  # (3,N)
    Kinv = np.linalg.inv(Kp)
    d = Kinv @ uv1      # (3,N)

    # 应用投影仪的yaw和pitch旋转
    R_proj = rot_y(proj_yaw) @ rot_x(proj_pitch)  # 投影仪绕Y轴和X轴旋转
    d_rotated = R_proj @ d  # (3,N)

    dx, dy, dz = d_rotated

    # 投影仪光心
    Cp = np.array([0.0, 0.0, -D], dtype=np.float64).reshape(3,1)

    # 与 Z=0 平面交点
    t = D / dz
    P = Cp + d_rotated * t      # (3,N)
    return P.T          # (N,3)


# ---------- 从世界 3D -> 相机像素 ----------
def world_to_camera_pixel(P_w, Kc, dx, D, pitch, yaw, roll):
    """
    P_w: (N,3) 世界坐标点
    dx: 相机中心相对投影仪在 X 方向上的基线 (m)
    D:  与墙面的距离 (决定相机的 Z 坐标)
    返回: (N,2) 相机像素坐标
    """
    # 相机中心（与投影仪同 z = -D，x 上有基线 dx）
    C_cam = np.array([dx, 0.0, -D], dtype=np.float64).reshape(3,1)

    # camera-to-world
    R_c2w = build_rotation(pitch, yaw, roll)
    # world-to-camera
    R_w2c = R_c2w.T

    Pw = P_w.T                   # (3,N)
    Pc = R_w2c @ (Pw - C_cam)    # (3,N)
    Xc, Yc, Zc = Pc

    eps = 1e-9
    Zc = np.where(Zc < eps, eps, Zc)

    fx, fy = Kc[0,0], Kc[1,1]
    cx, cy = Kc[0,2], Kc[1,2]

    u = fx * (Xc / Zc) + cx
    v = fy * (Yc / Zc) + cy
    return np.vstack([u, v]).T   # (N,2)


# ---------- 固定参数：投影仪初始内参 + 基线 + 相机初始内参 ----------
@dataclass
class SystemFixed:
    Kp0: np.ndarray     # 投影仪初始内参（用来加增量）
    Kc0: np.ndarray     # 相机初始内参（用来加增量）
    dx: float           # 相机相对投影仪在 X 方向上的基线 (米)


# ---------- 残差函数：优化相机内参和投影仪yaw/pitch角度，投影仪cxp固定 ----------
def residual_params(theta, fixed: SystemFixed, proj_pts, cam_obs, pitch_fixed, yaw_fixed, roll_fixed, D_fixed):
    """
    theta: [d_fx, d_fy, d_cx, d_cy, proj_yaw, proj_pitch]
    fixed: 包含 Kp0, Kc0, dx
    proj_pts: (N,2)
    cam_obs:  (N,2)
    pitch_fixed, yaw_fixed, roll_fixed, D_fixed: 固定的相机姿态和距离参数
    返回 (2N,) 残差
    """
    d_fx, d_fy, d_cx, d_cy, proj_yaw, proj_pitch = theta
    pitch, yaw, roll, D = pitch_fixed, yaw_fixed, roll_fixed, D_fixed
    Kp0, Kc0, dx = fixed.Kp0, fixed.Kc0, fixed.dx

    # 投影仪内参保持固定（不再优化cxp）
    Kp = Kp0.copy()

    # 从增量恢复当前相机内参
    fx0, fy0 = Kc0[0,0], Kc0[1,1]
    cx0, cy0 = Kc0[0,2], Kc0[1,2]

    fx = fx0 + d_fx
    fy = fy0 + d_fy
    cx = cx0 + d_cx
    cy = cy0 + d_cy

    Kc = np.array([[fx, 0.0, cx],
                   [0.0, fy, cy],
                   [0.0, 0.0, 1.0]], dtype=np.float64)

    u_p = proj_pts[:,0]
    v_p = proj_pts[:,1]

    # 投影仪像素 -> 墙面 3D (考虑投影仪yaw和pitch角度)
    Pw = projector_pixel_to_world(u_p, v_p, Kp, D, proj_yaw, proj_pitch)  # (N,3)

    # 墙面 3D -> 相机像素
    cam_pred = world_to_camera_pixel(Pw, Kc, dx, D, pitch, yaw, roll)  # (N,2)

    res = (cam_pred - cam_obs).ravel()
    return res


if __name__ == '__main__':
    # import os
    # imgdir = "D:\Dataset\keystone"
    # corner_dict = get_point_more(None, None)

    
    # corner_new_dict = {}
    # for key in corner_dict.keys():
    #     image = cv2.imread(os.path.join(imgdir, key))
    #     corner = get_point_more(image, key)
    #     corner = np.reshape(corner, (5, 8, 2))

    #     # print(key, np.linalg.norm(corner[0, 0] - corner[0, 7]), np.linalg.norm(corner[4, 0] - corner[4, 7]), 
    #     #       np.linalg.norm(corner[0, 0] - corner[4, 0]), np.linalg.norm(corner[0, 7] - corner[4, 7]),
    #     #       np.linalg.norm(corner[0, 0] - corner[4, 0]) /np.linalg.norm(corner[0, 0] - corner[0, 7]),
    #     #       np.arctan((corner[4, 0][0] - corner[0, 0][0]) / (corner[4, 0][1] - corner[0, 0][1]) * 180 / np.pi), 
    #     #       np.arctan((corner[4, 7][0] - corner[0, 7][0]) / (corner[4, 7][1] - corner[0, 7][1]) * 180 / np.pi))

    #     corner_new_dict[key] = corner

    # point_100cm = corner_new_dict["cam_100cm_0d.jpg"]
    # point_200cm = corner_new_dict["cam_200cm_0d.jpg"]
    # point_150cm = corner_new_dict["cam_150cm_0d.jpg"]

    # for i in range(5):
    #     length_150 = np.linalg.norm(point_150cm[i, 0] - point_150cm[i, 7])
    #     length_100 = np.linalg.norm(point_100cm[i, 0] - point_100cm[i, 7])
    #     length_200 = np.linalg.norm(point_200cm[i, 0] - point_200cm[i, 7])
    #     print(1.0 + (length_150 - length_200) / ((length_100 - length_200) / 10) * 0.1)

    # quit()
    proj_pts = get_proj_pts()
    #proj_pts = np.repeat(proj_pts, 2, axis=0)
    N = proj_pts.shape[0]
    print("Number of chessboard points:", N)

    Kp = get_projector_K()
    Kc0 = get_cam_K()
    dx = 0.1


    # 读取真实相机观测
    image = cv2.imread("D:\Dataset\keystone\cam_200cm_0d.jpg")
    cam_obs = np.asarray(get_point(image, "cam_200cm_0d.jpg")) 
    #image = cv2.imread("D:\Dataset\keystone\cam_100cm_0d.jpg")
    #cam_obs = np.asarray(get_point(image, "cam_100cm_0d.jpg")) # (N,2)

    # ---------- 固定参数 ----------
    # 固定相机姿态和距离参数
    pitch_fixed = 17.0  # 固定相机pitch角度
    yaw_fixed = -2.0    # 固定相机yaw角度
    roll_fixed = 0.0    # 固定相机roll角度
    D_fixed = 2.0       # 固定距离

    print("Fixed parameters:")
    print(f"  Camera pitch: {pitch_fixed} deg")
    print(f"  Camera yaw: {yaw_fixed} deg")
    print(f"  Camera roll: {roll_fixed} deg")
    print(f"  Distance D: {D_fixed} m")
    print(f"  Projector cxp: {Kp[0,2]:.2f} (fixed)")

    # ---------- 初始猜测 ----------
    # 优化相机内参增量和投影仪yaw/pitch角度，从 0 开始
    theta0 = np.array([0.0, 0.0,       # d_fx, d_fy
                       0.0, 0.0,       # d_cx, d_cy
                       0.0, 0.0],      # proj_yaw, proj_pitch
                      dtype=np.float64)
    print("Initial guess [d_fx,d_fy,d_cx,d_cy,proj_yaw,proj_pitch]:", theta0)

    # ---------- 约束 ----------
    # 对相机内参增量和投影仪角度设置约束
    fx0, fy0 = Kc0[0,0], Kc0[1,1]
    cx0, cy0 = Kc0[0,2], Kc0[1,2]

    # 相机焦距允许 ±20%
    dfx_min, dfx_max = -0.2 * fx0, 0.2 * fx0
    dfy_min, dfy_max = -0.2 * fy0, 0.2 * fy0

    # 相机主点允许偏移 ±50 像素
    dcx_min, dcx_max = -50.0, 50.0
    dcy_min, dcy_max = -50.0, 50.0

    # 投影仪yaw角度允许范围 ±30 度
    proj_yaw_min, proj_yaw_max = -30.0, 30.0

    # 投影仪pitch角度允许范围 ±30 度
    proj_pitch_min, proj_pitch_max = -30.0, 30.0

    lower = np.array([
        dfx_min, dfy_min,
        dcx_min, dcy_min,
        proj_yaw_min, proj_pitch_min
    ], dtype=np.float64)

    upper = np.array([
        dfx_max, dfy_max,
        dcx_max, dcy_max,
        proj_yaw_max, proj_pitch_max
    ], dtype=np.float64)

    fixed = SystemFixed(Kp0=Kp, Kc0=Kc0, dx=dx)

    # ---------- 优化 ----------
    result = least_squares(
        residual_params,
        theta0,
        args=(fixed, proj_pts, cam_obs, pitch_fixed, yaw_fixed, roll_fixed, D_fixed),
        method="trf",
        bounds=(lower, upper),
        verbose=2
    )

    d_fx_est, d_fy_est, d_cx_est, d_cy_est, proj_yaw_est, proj_pitch_est = result.x
    print("\nFixed camera angles (deg):", pitch_fixed, yaw_fixed, roll_fixed)
    print("Fixed D (m):", D_fixed)

    # 估计的相机内参
    fx_est = fx0 + d_fx_est
    fy_est = fy0 + d_fy_est
    cx_est = cx0 + d_cx_est
    cy_est = cy0 + d_cy_est

    Kc_est = np.array([[fx_est, 0.0, cx_est],
                       [0.0, fy_est, cy_est],
                       [0.0, 0.0, 1.0]], dtype=np.float64)

    # 投影仪内参保持固定
    Kp_est = Kp.copy()

    print("Estimated camera intrinsics Kc:")
    print(Kc_est)
    print("Fixed projector intrinsics Kp:")
    print(Kp_est)
    print(f"Estimated projector yaw angle: {proj_yaw_est:.3f} deg")
    print(f"Estimated projector pitch angle: {proj_pitch_est:.3f} deg")

    # ---------- 用估计参数重新投影并计算误差 ----------
    Pw_est = projector_pixel_to_world(proj_pts[:, 0], proj_pts[:, 1], Kp_est, D_fixed, proj_yaw_est, proj_pitch_est)
    cam_pred_est = world_to_camera_pixel(Pw_est, Kc_est, dx, D_fixed,
                                         pitch_fixed, yaw_fixed, roll_fixed)

    reproj_err = np.linalg.norm(cam_pred_est - cam_obs, axis=1)
    print("Mean reprojection error [pixels]:", reproj_err.mean())
    print("Max  reprojection error [pixels]:", reproj_err.max())

    # ---------- 可视化：红色真值，绿色重投影 ----------
    for i, (x, y) in enumerate(cam_obs):
        cv2.circle(image, (int(x), int(y)), 5, (0, 0, 255), -1)   # obs: red
        x1, y1 = cam_pred_est[i]
        cv2.circle(image, (int(x1), int(y1)), 5, (0, 255, 0), -1) # pred: green

    cv2.imshow("Chessboard with Corners (red=obs, green=pred)", image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    #quit()

    # Kc1 = np.array([[859.79920789,  0. , 628.59410574],
    #                 [0. , 856.97630659, 383.38686511],
    #                 [0. ,  0. ,          1.        ]], dtype=np.float32)
    # Kp1 = np.array([[1.536e+03, 0.000e+00, 9.090e+02],
    #                 [0.000e+00, 1.536e+03, 1.080e+03],
    #                 [0.000e+00, 0.000e+00, 1.000e+00]], dtype=np.float32)
    
    # Kc2 = np.array([[862.49804941, 0., 627.88973989],
    #                 [0., 861.24975194, 381.0985397 ],
    #                 [0.,  0. ,     1.        ]], dtype=np.float32)
    # Kp2 = np.array([[1.536e+03, 0.000e+00, 9.090e+02],
    #                 [0.000e+00, 1.536e+03, 1.080e+03],
    #                 [0.000e+00, 0.000e+00, 1.000e+00]], dtype=np.float32)
    
    # Kp_est = (Kp1 + Kp2) / 2
    # Kc_est = (Kc1 + Kc2) / 2

    # ---------- 计算在新的D距离下投影仪角点在相机中的成像点 ----------
    def compute_camera_projection_at_new_distance(proj_pts, Kp_est, Kc_est, dx,
                                                  pitch_fixed, yaw_fixed, roll_fixed,
                                                  D_new, proj_yaw_est, proj_pitch_est):
        """
        使用拟合出来的投影仪与相机内参，计算在新的D距离下投影仪角点在相机中的成像点

        Args:
            proj_pts: (N,2) 投影仪角点像素坐标
            Kp_est: (3,3) 估计的投影仪内参矩阵
            Kc_est: (3,3) 估计的相机内参矩阵
            dx: float 相机相对投影仪在X方向的基线
            pitch_fixed, yaw_fixed, roll_fixed: 固定的相机姿态角度
            D_new: float 新的距离
            proj_yaw_est: float 估计的投影仪yaw角度
            proj_pitch_est: float 估计的投影仪pitch角度

        Returns:
            cam_pts_new: (N,2) 相机中的成像点坐标
        """
        # 投影仪像素 -> 新距离下的墙面3D点 (考虑投影仪yaw和pitch角度)
        Pw_new = projector_pixel_to_world(proj_pts[:, 0], proj_pts[:, 1], Kp_est, D_new, proj_yaw_est, proj_pitch_est)

        # 墙面3D点 -> 相机像素
        cam_pts_new = world_to_camera_pixel(Pw_new, Kc_est, dx, D_new,
                                           pitch_fixed, yaw_fixed, roll_fixed)

        return cam_pts_new

    # 示例：计算在不同距离下的成像点
    distances_to_test = [1.5]  # 测试不同的距离

    print("\n" + "="*60)
    print("计算在不同距离下投影仪角点在相机中的成像点:")
    print("="*60)

    for D_new in distances_to_test:
        cam_pts_new = compute_camera_projection_at_new_distance(
            proj_pts, Kp_est, Kc_est, dx,
            pitch_fixed, yaw_fixed, roll_fixed, D_new, proj_yaw_est, proj_pitch_est
        )

        image = cv2.imread(f"D:\Dataset\keystone\cam_150cm_0d.jpg")
        cam_obs = np.asarray(get_point(image, "cam_150cm_0d.jpg"))
        reproj_err_new = np.linalg.norm(cam_pts_new - cam_obs, axis=1)
        print(f"  重投影误差 [pixels]: mean={reproj_err_new.mean():.2f}, max={reproj_err_new.max():.2f}")

        print(f"\n距离 D = {D_new:.1f}m 时:")
        print(f"  前5个角点的相机成像坐标:")
        for i in range(len(cam_pts_new)):
            u, v = cam_pts_new[i]
            print(f"    点{i+1}: ({u:.2f}, {v:.2f}), ({cam_obs[i, 0]:.2f}, {cam_obs[i, 1]:.2f})")

        # 计算成像点的范围
        u_min, u_max = cam_pts_new[:, 0].min(), cam_pts_new[:, 0].max()
        v_min, v_max = cam_pts_new[:, 1].min(), cam_pts_new[:, 1].max()
        print(f"  成像范围: u=[{u_min:.1f}, {u_max:.1f}], v=[{v_min:.1f}, {v_max:.1f}]")

        # 检查是否在相机视野内（假设相机分辨率为1280x720）
        cam_width, cam_height = 1280, 720
        in_view = ((cam_pts_new[:, 0] >= 0) & (cam_pts_new[:, 0] < cam_width) &
                   (cam_pts_new[:, 1] >= 0) & (cam_pts_new[:, 1] < cam_height))
        in_view_count = in_view.sum()
        print(f"  在相机视野内的点数: {in_view_count}/{len(cam_pts_new)} ({100*in_view_count/len(cam_pts_new):.1f}%)")

    # 返回函数供外部使用
    print(f"\n可以使用 compute_camera_projection_at_new_distance() 函数计算任意距离下的成像点")

    # 将函数添加到全局命名空间，方便后续使用
    globals()['compute_camera_projection_at_new_distance'] = compute_camera_projection_at_new_distance

    # ---------- 新增功能：拟合投影仪yaw角度 ----------
    def fit_projector_yaw_angle(proj_pts, cam_obs, Kp_est, Kc_est, dx,
                                pitch_fixed, yaw_fixed, roll_fixed, D_given):
        """
        使用估计出来的内参，通过给定的距离和相机观测点来拟合投影仪的yaw角度

        Args:
            proj_pts: (N,2) 投影仪角点像素坐标
            cam_obs: (N,2) 相机中观测到的角点坐标
            Kp_est: (3,3) 估计的投影仪内参矩阵
            Kc_est: (3,3) 估计的相机内参矩阵
            dx: float 相机相对投影仪在X方向的基线
            pitch_fixed, yaw_fixed, roll_fixed: 固定的相机姿态角度
            D_given: float 给定的投影仪到墙面距离

        Returns:
            proj_yaw_est: float 估计的投影仪yaw角度
            residual_final: float 最终的重投影误差
        """
        def residual_proj_yaw(proj_yaw):
            """
            投影仪yaw角度的残差函数
            """
            # 使用当前的投影仪yaw角度计算重投影（pitch角度设为0）
            Pw = projector_pixel_to_world(proj_pts[:, 0], proj_pts[:, 1],
                                        Kp_est, D_given, proj_yaw[0], 0.0)
            cam_pred = world_to_camera_pixel(Pw, Kc_est, dx, D_given,
                                           pitch_fixed, yaw_fixed, roll_fixed)

            # 计算残差
            residual = (cam_pred - cam_obs).ravel()
            return residual

        # 初始猜测：投影仪yaw角度为0度
        proj_yaw_init = np.array([0.0])

        # 设置约束：投影仪yaw角度范围 [-30, 30] 度
        bounds = ([-30.0], [30.0])

        # 优化投影仪yaw角度
        result = least_squares(
            residual_proj_yaw,
            proj_yaw_init,
            #bounds=bounds,
            method="lm",
            verbose=1,
            ftol=1e-10, xtol=1e-10,gtol=1e-10
        )

        proj_yaw_est = result.x[0]
        residual_final = np.sqrt(np.mean(result.fun**2))  # RMS误差

        return proj_yaw_est, residual_final

    # ---------- 测试投影仪yaw角度拟合功能 ----------
    print("\n" + "="*60)
    print("测试投影仪yaw角度拟合功能:")
    print("="*60)

    proj_pts = get_proj_pts_more()

    # 使用不同距离的数据进行测试
    test_cases = [
        {"distance": 1.5, "image_file": "cam_150cm_0d.jpg"},
        {"distance": 1.5, "image_file": "cam_150cm_10d_r.jpg"},
        {"distance": 1.5, "image_file": "cam_150cm_15d_l.jpg"},
        {"distance": 1.5, "image_file": "cam_150cm_10d_r.jpg"},
        {"distance": 1.5, "image_file": "cam_150cm_20d_r.jpg"}
        # {"distance": 1.0, "image_file": "cam_100cm_0d.jpg"},
        # {"distance": 1.2, "image_file": "cam_120cm_0d.jpg"}
    ]

    for test_case in test_cases:
        D_test = test_case["distance"]
        img_file = test_case["image_file"]

        try:
            # 读取对应距离的相机观测数据
            test_image = cv2.imread(f"D:\\Dataset\\keystone\\{img_file}")
            if test_image is None:
                print(f"  跳过测试 {img_file}：文件不存在")
                continue

            cam_obs_test = np.asarray(get_point_more(test_image, img_file))

            print(f"\n测试距离 D = {D_test:.1f}m ({img_file}):")

            # 拟合投影仪yaw角度
            proj_yaw_est, residual_final = fit_projector_yaw_angle(
                proj_pts, cam_obs_test, Kp_est, Kc_est, dx,
                pitch_fixed, yaw_fixed, roll_fixed, D_test
            )

            print(f"  估计的投影仪yaw角度: {proj_yaw_est:.3f}°")
            print(f"  最终重投影误差 (RMS): {residual_final:.3f} pixels")

            # 验证结果：使用估计的yaw角度重新计算重投影（pitch角度设为0）
            Pw_verify = projector_pixel_to_world(proj_pts[:, 0], proj_pts[:, 1],
                                               Kp_est, D_test, proj_yaw_est, 0.0)
            cam_pred_verify = world_to_camera_pixel(Pw_verify, Kc_est, dx, D_test,
                                                  pitch_fixed, yaw_fixed, roll_fixed)

            # 计算各点的重投影误差
            point_errors = np.linalg.norm(cam_pred_verify - cam_obs_test, axis=1)
            print(f"  平均重投影误差: {point_errors.mean():.3f} pixels")
            print(f"  最大重投影误差: {point_errors.max():.3f} pixels")

        except Exception as e:
            print(f"  测试 {img_file} 时出错: {str(e)}")

    # 将拟合函数添加到全局命名空间
    globals()['fit_projector_yaw_angle'] = fit_projector_yaw_angle

    print(f"\n可以使用 fit_projector_yaw_angle() 函数拟合投影仪yaw角度")

    # ---------- 新增功能：拟合投影仪到墙面的距离 ----------
    def fit_projector_distance(proj_pts, cam_obs, Kp_est, Kc_est, dx,
                              pitch_fixed, yaw_fixed, roll_fixed, proj_yaw_given, proj_pitch_given=0.0):
        """
        使用估计出来的内参，通过给定的投影仪yaw和pitch角度和相机观测点来拟合投影仪到墙面的距离

        Args:
            proj_pts: (N,2) 投影仪角点像素坐标
            cam_obs: (N,2) 相机中观测到的角点坐标
            Kp_est: (3,3) 估计的投影仪内参矩阵
            Kc_est: (3,3) 估计的相机内参矩阵
            dx: float 相机相对投影仪在X方向的基线
            pitch_fixed, yaw_fixed, roll_fixed: 固定的相机姿态角度
            proj_yaw_given: float 给定的投影仪yaw角度
            proj_pitch_given: float 给定的投影仪pitch角度

        Returns:
            D_est: float 估计的投影仪到墙面距离
            residual_final: float 最终的重投影误差
        """
        def residual_distance(D):
            """
            投影仪距离的残差函数
            """
            # 使用当前的距离计算重投影
            Pw = projector_pixel_to_world(proj_pts[:, 0], proj_pts[:, 1],
                                        Kp_est, D[0], proj_yaw_given, proj_pitch_given)
            cam_pred = world_to_camera_pixel(Pw, Kc_est, dx, D[0],
                                           pitch_fixed, yaw_fixed, roll_fixed)

            # 计算残差
            residual = (cam_pred - cam_obs).ravel()
            return residual

        # 初始猜测：距离为2.0米
        D_init = np.array([2.0])

        # 设置约束：距离范围 [0.5, 5.0] 米
        bounds = ([0.5], [5.0])

        # 优化投影仪到墙面的距离
        result = least_squares(
            residual_distance,
            D_init,
            bounds=bounds,
            method="trf",
            verbose=1
        )

        D_est = result.x[0]
        residual_final = np.sqrt(np.mean(result.fun**2))  # RMS误差

        return D_est, residual_final

    # ---------- 测试投影仪距离拟合功能 ----------
    print("\n" + "="*60)
    print("测试投影仪距离拟合功能:")
    print("="*60)

    proj_pts = get_proj_pts()

    # 使用不同距离的数据进行测试，假设投影仪yaw角度为0度
    test_cases_distance = [
        {"true_distance": 1.5, "image_file": "cam_150cm_0d.jpg"},
        {"true_distance": 1.0, "image_file": "cam_100cm_0d.jpg"},
        {"true_distance": 1.2, "image_file": "cam_120cm_0d.jpg"},
        {"true_distance": 2.0, "image_file": "cam_200cm_0d.jpg"}
    ]

    # 假设投影仪yaw角度为0度（可以根据实际情况调整）
    proj_yaw_assumed = 0.0

    for test_case in test_cases_distance:
        D_true = test_case["true_distance"]
        img_file = test_case["image_file"]

        try:
            # 读取对应距离的相机观测数据
            test_image = cv2.imread(f"D:\\Dataset\\keystone\\{img_file}")
            if test_image is None:
                print(f"  跳过测试 {img_file}：文件不存在")
                continue

            cam_obs_test = np.asarray(get_point(test_image, img_file))

            print(f"\n测试真实距离 D = {D_true:.1f}m ({img_file}):")
            print(f"  假设投影仪yaw角度: {proj_yaw_assumed:.1f}°")

            # 拟合投影仪到墙面的距离
            D_est, residual_final = fit_projector_distance(
                proj_pts, cam_obs_test, Kp_est, Kc_est, dx,
                pitch_fixed, yaw_fixed, roll_fixed, proj_yaw_assumed
            )

            print(f"  估计的投影仪距离: {D_est:.3f}m")
            print(f"  真实距离: {D_true:.3f}m")
            print(f"  距离误差: {abs(D_est - D_true):.3f}m ({abs(D_est - D_true)/D_true*100:.1f}%)")
            print(f"  最终重投影误差 (RMS): {residual_final:.3f} pixels")

            # 验证结果：使用估计的距离重新计算重投影（pitch角度设为0）
            Pw_verify = projector_pixel_to_world(proj_pts[:, 0], proj_pts[:, 1],
                                               Kp_est, D_est, proj_yaw_assumed, 0.0)
            cam_pred_verify = world_to_camera_pixel(Pw_verify, Kc_est, dx, D_est,
                                                  pitch_fixed, yaw_fixed, roll_fixed)

            # 计算各点的重投影误差
            point_errors = np.linalg.norm(cam_pred_verify - cam_obs_test, axis=1)
            print(f"  平均重投影误差: {point_errors.mean():.3f} pixels")
            print(f"  最大重投影误差: {point_errors.max():.3f} pixels")

        except Exception as e:
            print(f"  测试 {img_file} 时出错: {str(e)}")

    # 将拟合函数添加到全局命名空间
    globals()['fit_projector_distance'] = fit_projector_distance

    print(f"\n可以使用 fit_projector_distance() 函数拟合投影仪到墙面的距离")