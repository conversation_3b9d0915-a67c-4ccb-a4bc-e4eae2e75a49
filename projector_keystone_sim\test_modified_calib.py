#!/usr/bin/env python3
"""
测试修改后的标定代码
验证投影仪yaw和pitch角度作为拟合参数，cxp作为固定参数的功能
"""

import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_projector_pixel_to_world():
    """测试投影仪像素到世界坐标的转换函数"""
    print("测试 projector_pixel_to_world 函数...")
    
    # 导入修改后的函数
    from calib_real_at_0_angle_3 import projector_pixel_to_world, get_projector_K
    
    # 获取投影仪内参
    Kp = get_projector_K()
    print(f"投影仪内参矩阵:\n{Kp}")
    
    # 测试参数
    u = np.array([960.0])  # 投影仪中心像素
    v = np.array([540.0])
    D = 2.0  # 距离2米
    proj_yaw = 10.0  # yaw角度10度
    proj_pitch = 5.0  # pitch角度5度
    
    # 调用函数
    world_pts = projector_pixel_to_world(u, v, Kp, D, proj_yaw, proj_pitch)
    print(f"投影仪像素 ({u[0]}, {v[0]}) -> 世界坐标: {world_pts[0]}")
    
    # 测试不同角度的影响
    world_pts_no_angle = projector_pixel_to_world(u, v, Kp, D, 0.0, 0.0)
    print(f"无角度时的世界坐标: {world_pts_no_angle[0]}")
    
    print("✓ projector_pixel_to_world 函数测试通过\n")

def test_residual_params():
    """测试残差函数的参数格式"""
    print("测试 residual_params 函数参数格式...")
    
    from calib_real_at_0_angle_3 import residual_params, SystemFixed, get_projector_K, get_cam_K, get_proj_pts
    
    # 准备测试数据
    Kp = get_projector_K()
    Kc0 = get_cam_K()
    dx = 0.1
    fixed = SystemFixed(Kp0=Kp, Kc0=Kc0, dx=dx)
    
    proj_pts = get_proj_pts()[:5]  # 只取前5个点进行测试
    cam_obs = proj_pts + np.random.normal(0, 1, proj_pts.shape)  # 模拟观测数据
    
    # 测试参数：[d_fx, d_fy, d_cx, d_cy, proj_yaw, proj_pitch]
    theta = np.array([0.0, 0.0, 0.0, 0.0, 5.0, 2.0])
    
    # 固定参数
    pitch_fixed = 17.0
    yaw_fixed = -2.0
    roll_fixed = 0.0
    D_fixed = 2.0
    
    try:
        # 调用残差函数
        residuals = residual_params(theta, fixed, proj_pts, cam_obs, 
                                  pitch_fixed, yaw_fixed, roll_fixed, D_fixed)
        print(f"残差函数返回形状: {residuals.shape}")
        print(f"残差均方根: {np.sqrt(np.mean(residuals**2)):.3f}")
        print("✓ residual_params 函数测试通过\n")
    except Exception as e:
        print(f"✗ residual_params 函数测试失败: {e}\n")

def main():
    """主测试函数"""
    print("="*60)
    print("测试修改后的投影仪标定代码")
    print("="*60)
    
    test_projector_pixel_to_world()
    test_residual_params()
    
    print("="*60)
    print("所有测试完成！")
    print("="*60)

if __name__ == "__main__":
    main()
